// import { logger } from '../config/logger';
import { circuitBreakerRegistry } from '../utils/circuit-breaker';
import mongoose from 'mongoose';

interface MetricData {
  name: string;
  value: number;
  tags: Record<string, string>;
  timestamp: Date;
}

interface HealthCheckResult {
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  details?: any;
  error?: string;
}

class MonitoringService {
  private metrics: Map<string, MetricData[]> = new Map();
  private readonly maxMetricsPerType = 1000;
  private readonly metricsRetentionMs = 24 * 60 * 60 * 1000; // 24 hours

  // record metrics
  recordMetric(name: string, value: number, tags?: Record<string, string>): void {
    const metric: MetricData = {
      name,
      value,
      tags: tags || {},
      timestamp: new Date()
    };

    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    const metricsList = this.metrics.get(name)!;
    metricsList.push(metric);

    // cleanup old metrics
    this.cleanupOldMetrics(name);
  }

  // record response time
  recordResponseTime(endpoint: string, method: string, statusCode: number, responseTime: number): void {
    this.recordMetric('http_request_duration', responseTime, {
      endpoint,
      method,
      status_code: statusCode.toString()
    });

    this.recordMetric('http_request_count', 1, {
      endpoint,
      method,
      status_code: statusCode.toString()
    });
  }

  // record database operation
  recordDbOperation(operation: string, collection: string, duration: number, success: boolean): void {
    this.recordMetric('db_operation_duration', duration, {
      operation,
      collection,
      success: success.toString()
    });

    this.recordMetric('db_operation_count', 1, {
      operation,
      collection,
      success: success.toString()
    });
  }

  // record external API call
  recordExternalApiCall(service: string, endpoint: string, duration: number, success: boolean): void {
    this.recordMetric('external_api_duration', duration, {
      service,
      endpoint,
      success: success.toString()
    });

    this.recordMetric('external_api_count', 1, {
      service,
      endpoint,
      success: success.toString()
    });
  }

  // get metrics summary
  getMetricsSummary(name: string, timeRangeMs: number = 60000): any {
    const metrics = this.metrics.get(name) || [];
    const cutoffTime = new Date(Date.now() - timeRangeMs);
    
    const recentMetrics = metrics.filter(m => m.timestamp >= cutoffTime);
    
    if (recentMetrics.length === 0) {
      return { count: 0, avg: 0, min: 0, max: 0, sum: 0 };
    }

    const values = recentMetrics.map(m => m.value);
    const sum = values.reduce((a, b) => a + b, 0);
    const avg = sum / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);

    return {
      count: recentMetrics.length,
      avg: Math.round(avg * 100) / 100,
      min,
      max,
      sum: Math.round(sum * 100) / 100,
      timeRange: `${timeRangeMs / 1000}s`
    };
  }

  // comprehensive health check
  async performHealthCheck(): Promise<{
    overall: 'healthy' | 'degraded' | 'unhealthy';
    checks: HealthCheckResult[];
    timestamp: Date;
  }> {
    const checks: HealthCheckResult[] = [];

    // database health
    checks.push(await this.checkDatabase());
    
    // memory health
    checks.push(await this.checkMemory());
    
    // circuit breakers health
    checks.push(await this.checkCircuitBreakers());
    
    // response time health
    checks.push(await this.checkResponseTimes());

    // determine overall health
    const unhealthyCount = checks.filter(c => c.status === 'unhealthy').length;
    const degradedCount = checks.filter(c => c.status === 'degraded').length;

    let overall: 'healthy' | 'degraded' | 'unhealthy';
    if (unhealthyCount > 0) {
      overall = 'unhealthy';
    } else if (degradedCount > 0) {
      overall = 'degraded';
    } else {
      overall = 'healthy';
    }

    return {
      overall,
      checks,
      timestamp: new Date()
    };
  }

  private async checkDatabase(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      const db = mongoose.connection.db;
      if (!db) {
        throw new Error('database connection not available');
      }

      await db.admin().ping();
      const responseTime = Date.now() - startTime;

      return {
        name: 'database',
        status: responseTime < 100 ? 'healthy' : responseTime < 500 ? 'degraded' : 'unhealthy',
        responseTime,
        details: {
          readyState: mongoose.connection.readyState,
          host: mongoose.connection.host,
          port: mongoose.connection.port
        }
      };
    } catch (error: any) {
      return {
        name: 'database',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  private async checkMemory(): Promise<HealthCheckResult> {
    const memUsage = process.memoryUsage();
    const heapUsedPercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;

    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (heapUsedPercent < 70) {
      status = 'healthy';
    } else if (heapUsedPercent < 90) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }

    return {
      name: 'memory',
      status,
      responseTime: 0,
      details: {
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
        heapUsedPercent: Math.round(heapUsedPercent),
        external: Math.round(memUsage.external / 1024 / 1024),
        rss: Math.round(memUsage.rss / 1024 / 1024)
      }
    };
  }

  private async checkCircuitBreakers(): Promise<HealthCheckResult> {
    const breakerStats = circuitBreakerRegistry.getAllStats();
    const openBreakers = breakerStats.filter(b => b.state === 'open');
    const halfOpenBreakers = breakerStats.filter(b => b.state === 'half_open');

    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (openBreakers.length === 0) {
      status = halfOpenBreakers.length === 0 ? 'healthy' : 'degraded';
    } else {
      status = 'unhealthy';
    }

    return {
      name: 'circuit_breakers',
      status,
      responseTime: 0,
      details: {
        total: breakerStats.length,
        open: openBreakers.length,
        halfOpen: halfOpenBreakers.length,
        closed: breakerStats.filter(b => b.state === 'closed').length,
        openBreakers: openBreakers.map(b => b.name)
      }
    };
  }

  private async checkResponseTimes(): Promise<HealthCheckResult> {
    const responseTimeMetrics = this.getMetricsSummary('http_request_duration', 300000); // 5 minutes
    
    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (responseTimeMetrics.avg < 200) {
      status = 'healthy';
    } else if (responseTimeMetrics.avg < 1000) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }

    return {
      name: 'response_times',
      status,
      responseTime: 0,
      details: responseTimeMetrics
    };
  }

  private cleanupOldMetrics(name: string): void {
    const metricsList = this.metrics.get(name)!;
    const cutoffTime = new Date(Date.now() - this.metricsRetentionMs);
    
    // remove old metrics
    const filteredMetrics = metricsList.filter(m => m.timestamp >= cutoffTime);
    
    // limit number of metrics
    if (filteredMetrics.length > this.maxMetricsPerType) {
      filteredMetrics.splice(0, filteredMetrics.length - this.maxMetricsPerType);
    }
    
    this.metrics.set(name, filteredMetrics);
  }

  // get system stats
  getSystemStats(): any {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    return {
      uptime: process.uptime(),
      memory: {
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
        external: Math.round(memUsage.external / 1024 / 1024),
        rss: Math.round(memUsage.rss / 1024 / 1024)
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      nodeVersion: process.version,
      platform: process.platform,
      pid: process.pid
    };
  }

  // alert thresholds check
  checkAlertThresholds(): any[] {
    const alerts: any[] = [];
    
    // check response time alerts
    const responseTimeMetrics = this.getMetricsSummary('http_request_duration', 300000);
    if (responseTimeMetrics.avg > 2000) {
      alerts.push({
        type: 'high_response_time',
        severity: 'warning',
        message: `Average response time is ${responseTimeMetrics.avg}ms`,
        threshold: 2000,
        current: responseTimeMetrics.avg
      });
    }

    // check error rate alerts
    // TODO: implement error rate calculation from status codes

    // check memory alerts
    const memUsage = process.memoryUsage();
    const heapUsedPercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
    if (heapUsedPercent > 85) {
      alerts.push({
        type: 'high_memory_usage',
        severity: heapUsedPercent > 95 ? 'critical' : 'warning',
        message: `Memory usage is ${heapUsedPercent.toFixed(1)}%`,
        threshold: 85,
        current: heapUsedPercent
      });
    }

    return alerts;
  }
}

export const monitoringService = new MonitoringService();
