import { WalletService } from './wallet.service';
import { UserService } from './user.service';
import { Transaction } from '../models/transaction.model';
import { TransactionType, TransactionStatus, Currency } from '../types';
import { CryptoUtils } from '../utils/crypto';
import { logger } from '../config/logger';
import mongoose from 'mongoose';

export class TransferService {
  static async sendMoney(data: {
    senderId: string;
    recipientId?: string;
    recipientEmail?: string;
    recipientPhone?: string;
    amount: number;
    currency: Currency;
    description?: string;
    pin: string;
    metadata?: any;
  }): Promise<{
    success: boolean;
    transferId: string;
    transactionRef: string;
    amount: number;
    fee: number;
    totalAmount: number;
    recipient: any;
    estimatedDelivery: string;
    status: string;
  }> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      // get sender
      const sender = await UserService.getUserById(data.senderId);
      if (!sender) {
        throw new Error('sender not found');
      }

      // find recipient
      let recipient;
      if (data.recipientId) {
        recipient = await UserService.getUserById(data.recipientId);
      } else if (data.recipientEmail) {
        recipient = await UserService.getUserByEmail(data.recipientEmail);
      } else if (data.recipientPhone) {
        recipient = await UserService.getUserByPhone(data.recipientPhone);
      }

      if (!recipient) {
        throw new Error('recipient not found');
      }

      // get sender wallet
      const senderWallet = await WalletService.getDefaultWallet(data.senderId, data.currency);
      if (!senderWallet) {
        throw new Error('sender wallet not found');
      }

      // get recipient wallet
      const recipientWallet = await WalletService.getDefaultWallet(recipient._id.toString(), data.currency);
      if (!recipientWallet) {
        throw new Error('recipient wallet not found');
      }

      // calculate fee
      const fee = this.calculateTransferFee(data.amount, data.currency);
      const totalAmount = data.amount + fee;

      // check balance
      if (senderWallet.available_balance < totalAmount) {
        throw new Error('insufficient balance');
      }

      // TODO: verify PIN
      
      // perform transfer
      const { fromTransaction } = await WalletService.transferBetweenWallets({
        fromWalletId: senderWallet._id.toString(),
        toWalletId: recipientWallet._id.toString(),
        amount: data.amount,
        description: data.description || 'P2P transfer',
        metadata: {
          ...data.metadata,
          fee,
          transferType: 'p2p',
          senderName: `${sender.first_name} ${sender.last_name}`,
          recipientName: `${recipient.first_name} ${recipient.last_name}`
        }
      });

      // deduct fee from sender
      if (fee > 0) {
        await WalletService.debitWallet({
          walletId: senderWallet._id.toString(),
          amount: fee,
          description: 'Transfer fee',
          transactionType: TransactionType.FEE,
          metadata: { relatedTransfer: fromTransaction._id }
        });
      }

      await session.commitTransaction();

      logger.info('p2p transfer completed', {
        senderId: data.senderId,
        recipientId: recipient._id,
        amount: data.amount,
        fee,
        transactionRef: fromTransaction.transaction_ref
      });

      return {
        success: true,
        transferId: fromTransaction._id.toString(),
        transactionRef: fromTransaction.transaction_ref,
        amount: data.amount,
        fee,
        totalAmount,
        recipient: {
          id: recipient._id.toString(),
          name: `${recipient.first_name} ${recipient.last_name}`,
          email: recipient.email,
          phone: recipient.phone
        },
        estimatedDelivery: 'instant',
        status: 'completed'
      };
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('p2p transfer failed:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async sendRemittance(data: {
    senderId: string;
    recipientCountry: string;
    recipientName: string;
    recipientPhone: string;
    amount: number;
    sourceCurrency: Currency;
    targetCurrency: Currency;
    deliveryMethod: 'bank' | 'mobile_money' | 'cash_pickup';
    bankDetails?: any;
    description?: string;
    metadata?: any;
  }): Promise<{
    success: boolean;
    remittanceId: string;
    referenceNumber: string;
    sourceAmount: number;
    targetAmount: number;
    exchangeRate: number;
    fee: number;
    totalCost: number;
    recipient: any;
    estimatedDelivery: string;
    trackingCode: string;
  }> {
    try {
      // get sender
      const sender = await UserService.getUserById(data.senderId);
      if (!sender) {
        throw new Error('sender not found');
      }

      // get sender wallet
      const senderWallet = await WalletService.getDefaultWallet(data.senderId, data.sourceCurrency);
      if (!senderWallet) {
        throw new Error('sender wallet not found');
      }

      // get exchange rate
      const exchangeRate = await this.getExchangeRate(data.sourceCurrency, data.targetCurrency);
      const targetAmount = data.amount * exchangeRate;
      
      // calculate fees
      const fee = this.calculateRemittanceFee(data.amount, data.sourceCurrency, data.targetCurrency);
      const totalCost = data.amount + fee;

      // check balance
      if (senderWallet.available_balance < totalCost) {
        throw new Error('insufficient balance');
      }

      // create remittance transaction
      const transaction = new Transaction({
        transaction_ref: CryptoUtils.generateTransactionRef('REM'),
        user_id: data.senderId,
        wallet_id: senderWallet._id,
        type: TransactionType.TRANSFER,
        status: TransactionStatus.PROCESSING,
        amount: data.amount,
        fee,
        currency: data.sourceCurrency,
        description: data.description || 'International remittance',
        balance_before: senderWallet.balance,
        balance_after: senderWallet.balance - totalCost,
        metadata: {
          ...data.metadata,
          remittanceType: 'international',
          recipientCountry: data.recipientCountry,
          recipientName: data.recipientName,
          recipientPhone: data.recipientPhone,
          targetCurrency: data.targetCurrency,
          targetAmount,
          exchangeRate,
          deliveryMethod: data.deliveryMethod,
          bankDetails: data.bankDetails
        }
      });

      await transaction.save();

      // debit sender wallet
      await WalletService.debitWallet({
        walletId: senderWallet._id.toString(),
        amount: totalCost,
        description: 'International remittance',
        transactionType: TransactionType.TRANSFER,
        externalRef: transaction.transaction_ref,
        metadata: transaction.metadata
      });

      const trackingCode = CryptoUtils.generateTransactionRef('TRACK');

      logger.info('remittance initiated', {
        senderId: data.senderId,
        amount: data.amount,
        targetAmount,
        recipientCountry: data.recipientCountry,
        transactionRef: transaction.transaction_ref
      });

      return {
        success: true,
        remittanceId: transaction._id.toString(),
        referenceNumber: transaction.transaction_ref,
        sourceAmount: data.amount,
        targetAmount,
        exchangeRate,
        fee,
        totalCost,
        recipient: {
          name: data.recipientName,
          phone: data.recipientPhone,
          country: data.recipientCountry,
          deliveryMethod: data.deliveryMethod
        },
        estimatedDelivery: this.getEstimatedDelivery(data.recipientCountry, data.deliveryMethod),
        trackingCode
      };
    } catch (error: any) {
      logger.error('remittance failed:', error);
      throw error;
    }
  }

  static async payBill(data: {
    userId: string;
    billType: 'electricity' | 'water' | 'internet' | 'mobile' | 'tv' | 'insurance';
    provider: string;
    accountNumber: string;
    amount: number;
    currency: Currency;
    metadata?: any;
  }): Promise<{
    success: boolean;
    paymentId: string;
    referenceNumber: string;
    amount: number;
    fee: number;
    totalAmount: number;
    provider: string;
    accountNumber: string;
    status: string;
    confirmationCode?: string;
  }> {
    try {
      // get user
      const user = await UserService.getUserById(data.userId);
      if (!user) {
        throw new Error('user not found');
      }

      // get user wallet
      const wallet = await WalletService.getDefaultWallet(data.userId, data.currency);
      if (!wallet) {
        throw new Error('wallet not found');
      }

      // calculate fee
      const fee = this.calculateBillPaymentFee(data.amount, data.billType);
      const totalAmount = data.amount + fee;

      // check balance
      if (wallet.available_balance < totalAmount) {
        throw new Error('insufficient balance');
      }

      // create bill payment transaction
      const transaction = new Transaction({
        transaction_ref: CryptoUtils.generateTransactionRef('BILL'),
        user_id: data.userId,
        wallet_id: wallet._id,
        type: TransactionType.PAYMENT,
        status: TransactionStatus.PROCESSING,
        amount: data.amount,
        fee,
        currency: data.currency,
        description: `${data.billType} bill payment - ${data.provider}`,
        balance_before: wallet.balance,
        balance_after: wallet.balance - totalAmount,
        external_provider: data.provider,
        external_reference: data.accountNumber,
        metadata: {
          ...data.metadata,
          billType: data.billType,
          provider: data.provider,
          accountNumber: data.accountNumber
        }
      });

      await transaction.save();

      // debit wallet
      await WalletService.debitWallet({
        walletId: wallet._id.toString(),
        amount: totalAmount,
        description: `Bill payment - ${data.provider}`,
        transactionType: TransactionType.PAYMENT,
        externalRef: transaction.transaction_ref,
        metadata: transaction.metadata
      });

      // TODO: integrate with actual bill payment providers
      const confirmationCode = CryptoUtils.generateRandomString(8).toUpperCase();

      // mark as completed (in real scenario, this would be async)
      transaction.status = TransactionStatus.COMPLETED;
      await transaction.save();

      logger.info('bill payment completed', {
        userId: data.userId,
        billType: data.billType,
        provider: data.provider,
        amount: data.amount,
        transactionRef: transaction.transaction_ref
      });

      return {
        success: true,
        paymentId: transaction._id.toString(),
        referenceNumber: transaction.transaction_ref,
        amount: data.amount,
        fee,
        totalAmount,
        provider: data.provider,
        accountNumber: data.accountNumber,
        status: 'completed',
        confirmationCode
      };
    } catch (error: any) {
      logger.error('bill payment failed:', error);
      throw error;
    }
  }

  private static calculateTransferFee(amount: number, currency: Currency): number {
    // simple fee calculation - 1% with minimum fee
    const percentage = 0.01;
    const minFee = currency === Currency.USD ? 0.5 : 100; // adjust based on currency
    return Math.max(amount * percentage, minFee);
  }

  private static calculateRemittanceFee(amount: number, sourceCurrency: Currency, targetCurrency: Currency): number {
    // remittance fees are typically higher
    const percentage = 0.025; // 2.5%
    const minFee = sourceCurrency === Currency.USD ? 2 : 500;
    const crossBorderFee = sourceCurrency !== targetCurrency ? 1 : 0;
    return Math.max(amount * percentage, minFee) + crossBorderFee;
  }

  private static calculateBillPaymentFee(amount: number, billType: string): number {
    // bill payment fees
    const feeMap: Record<string, number> = {
      electricity: 0.005, // 0.5%
      water: 0.005,
      internet: 0.01,
      mobile: 0.02,
      tv: 0.01,
      insurance: 0.015
    };
    
    const percentage = feeMap[billType] || 0.01;
    return Math.max(amount * percentage, 0.25);
  }

  private static async getExchangeRate(from: Currency, to: Currency): Promise<number> {
    // TODO: integrate with real exchange rate API
    const rates: Record<string, Record<string, number>> = {
      [Currency.USD]: { [Currency.RWF]: 1300, [Currency.KES]: 150, [Currency.EUR]: 0.85 },
      [Currency.EUR]: { [Currency.USD]: 1.18, [Currency.RWF]: 1534, [Currency.KES]: 177 },
      [Currency.RWF]: { [Currency.USD]: 0.00077, [Currency.EUR]: 0.00065, [Currency.KES]: 0.115 },
      [Currency.KES]: { [Currency.USD]: 0.0067, [Currency.EUR]: 0.0056, [Currency.RWF]: 8.67 }
    };

    return rates[from]?.[to] || 1;
  }

  private static getEstimatedDelivery(country: string, method: string): string {
    const deliveryTimes: Record<string, Record<string, string>> = {
      'RW': { bank: '1-2 hours', mobile_money: 'instant', cash_pickup: '30 minutes' },
      'KE': { bank: '2-4 hours', mobile_money: 'instant', cash_pickup: '1 hour' },
      'UG': { bank: '4-6 hours', mobile_money: '5 minutes', cash_pickup: '2 hours' },
      'TZ': { bank: '6-8 hours', mobile_money: '10 minutes', cash_pickup: '3 hours' }
    };

    return deliveryTimes[country]?.[method] || '1-2 business days';
  }
}
