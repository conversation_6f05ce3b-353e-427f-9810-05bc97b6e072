import { FastifyRequest, FastifyReply } from 'fastify';
import { monitoringService } from '../services/monitoring.service';
import { AuditLog } from '../models/audit-log.model';
import { AuditAction } from '../types';
import { logger } from '../config/logger';
import { v4 as uuidv4 } from 'uuid';

export class MonitoringMiddleware {
  static async requestTracking(request: FastifyRequest, _reply: FastifyReply): Promise<void> {
    const startTime = Date.now();
    const requestId = uuidv4();

    // add request ID to request object
    (request as any).requestId = requestId;
    (request as any).startTime = startTime;

    // log request start
    logger.info('request started', {
      requestId,
      method: request.method,
      url: request.url,
      ip: request.ip,
      userAgent: request.headers['user-agent']
    });
  }

  // audit logging middleware
  static async auditLogging(request: FastifyRequest, _reply: FastifyReply): Promise<void> {
    const requestId = (request as any).requestId || uuidv4();
    const user = (request as any).user;

    const auditAction = MonitoringMiddleware.getAuditAction(request.method, request.url);

    if (auditAction) {
      (request as any).auditData = {
        action: auditAction,
        userId: user?.id,
        resourceType: MonitoringMiddleware.getResourceType(request.url),
        resourceId: MonitoringMiddleware.extractResourceId(request.url, request.params),
        ipAddress: request.ip,
        userAgent: request.headers['user-agent'] || '',
        requestId,
        details: {
          method: request.method,
          url: request.url,
          body: MonitoringMiddleware.sanitizeRequestBody(request.body),
          query: request.query
        }
      };
    }
  }

  static async responseTracking(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    const startTime = (request as any).startTime || Date.now();
    const requestId = (request as any).requestId;
    const duration = Date.now() - startTime;

    monitoringService.recordResponseTime(
      request.url,
      request.method,
      reply.statusCode,
      duration
    );

    logger.info('request completed', {
      requestId,
      method: request.method,
      url: request.url,
      statusCode: reply.statusCode,
      duration,
      ip: request.ip
    });

    const auditData = (request as any).auditData;
    if (auditData) {
      try {
        await AuditLog.logAction({
          ...auditData,
          metadata: {
            endpoint: request.url,
            method: request.method,
            status_code: reply.statusCode,
            response_time: duration
          }
        });
      } catch (error) {
        logger.error('audit logging failed:', error);
      }
    }
  }

  static async errorTracking(error: Error, request: FastifyRequest, reply: FastifyReply): Promise<void> {
    const requestId = (request as any).requestId;
    const duration = Date.now() - ((request as any).startTime || Date.now());

    monitoringService.recordResponseTime(
      request.url,
      request.method,
      reply.statusCode || 500,
      duration
    );

    logger.error('request error', {
      requestId,
      method: request.method,
      url: request.url,
      error: error.message,
      stack: error.stack,
      ip: request.ip,
      userAgent: request.headers['user-agent'],
      duration
    });

    try {
      await AuditLog.logAction({
        action: AuditAction.ADMIN_ACTION, 
        userId: (request as any).user?.id,
        resourceType: 'error',
        ipAddress: request.ip,
        userAgent: request.headers['user-agent'] || '',
        requestId: requestId || uuidv4(),
        details: {
          error: error.message,
          method: request.method,
          url: request.url
        },
        metadata: {
          endpoint: request.url,
          method: request.method,
          status_code: reply.statusCode || 500,
          response_time: duration,
          error_message: error.message
        }
      });
    } catch (auditError) {
      logger.error('error audit logging failed:', auditError);
    }
  }

  private static getAuditAction(method: string, url: string): AuditAction | null {
    // map routes to audit actions
    const routeMap: Record<string, AuditAction> = {
      'POST /api/v1/auth/login': AuditAction.USER_LOGIN,
      'POST /api/v1/auth/logout': AuditAction.USER_LOGOUT,
      'POST /api/v1/auth/register': AuditAction.USER_REGISTER,
      'PUT /api/v1/users/profile': AuditAction.USER_UPDATE,
      'POST /api/v1/transfers/p2p': AuditAction.TRANSACTION_CREATE,
      'POST /api/v1/transfers/remittance': AuditAction.TRANSACTION_CREATE,
      'POST /api/v1/transfers/bill-payment': AuditAction.TRANSACTION_CREATE,
      'POST /api/v1/wallets/create': AuditAction.WALLET_CREATE,
      'POST /api/v1/agents/register': AuditAction.AGENT_REGISTER,
      'POST /api/v1/loans/apply': AuditAction.LOAN_APPLY,
      'POST /api/v1/savings/create': AuditAction.SAVINGS_CREATE
    };

    const key = `${method} ${url}`;
    return routeMap[key] || null;
  }

  private static getResourceType(url: string): string {
    if (url.includes('/users')) return 'user';
    if (url.includes('/transfers')) return 'transaction';
    if (url.includes('/wallets')) return 'wallet';
    if (url.includes('/agents')) return 'agent';
    if (url.includes('/loans')) return 'loan';
    if (url.includes('/savings')) return 'savings';
    if (url.includes('/admin')) return 'admin';
    return 'unknown';
  }

  private static extractResourceId(_url: string, params: any): string | undefined {
    if (params && typeof params === 'object') {
      return params.id || params.userId || params.transactionId || params.walletId;
    }
    return undefined;
  }

  private static sanitizeRequestBody(body: any): any {
    if (!body || typeof body !== 'object') return body;

    const sanitized = { ...body };
    
    const sensitiveFields = ['password', 'pin', 'token', 'secret', 'key', 'otp'];
    
    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  // rate limiting check
  static async rateLimitCheck(request: FastifyRequest, _reply: FastifyReply): Promise<void> {
    const ip = request.ip;
    const endpoint = request.url;

    // TODO: implement Redis-based rate limiting
    // For now, just log the attempt
    logger.debug('rate limit check', {
      ip,
      endpoint,
      userAgent: request.headers['user-agent']
    });
  }

  // security headers middleware
  static async securityHeaders(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    reply.header('X-Request-ID', (request as any).requestId || uuidv4());
    reply.header('X-Content-Type-Options', 'nosniff');
    reply.header('X-Frame-Options', 'DENY');
    reply.header('X-XSS-Protection', '1; mode=block');
    reply.header('Referrer-Policy', 'strict-origin-when-cross-origin');
    reply.header('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  }

  // health check for monitoring endpoints
  static async healthCheck(_request: FastifyRequest, reply: FastifyReply): Promise<any> {
    try {
      const healthResult = await monitoringService.performHealthCheck();
      
      const statusCode = healthResult.overall === 'healthy' ? 200 : 
                        healthResult.overall === 'degraded' ? 200 : 503;

      return reply.status(statusCode).send({
        status: healthResult.overall,
        timestamp: healthResult.timestamp,
        checks: healthResult.checks,
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0'
      });
    } catch (error: any) {
      logger.error('health check failed:', error);
      return reply.status(503).send({
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date()
      });
    }
  }

  // metrics endpoint
  static async getMetrics(request: FastifyRequest, reply: FastifyReply): Promise<any> {
    try {
      const { timeRange = 300000 } = request.query as any; // 5 minutes default

      const metrics = {
        response_times: monitoringService.getMetricsSummary('http_request_duration', timeRange),
        request_count: monitoringService.getMetricsSummary('http_request_count', timeRange),
        db_operations: monitoringService.getMetricsSummary('db_operation_duration', timeRange),
        external_apis: monitoringService.getMetricsSummary('external_api_duration', timeRange),
        system_stats: monitoringService.getSystemStats(),
        alerts: monitoringService.checkAlertThresholds()
      };

      return reply.send({
        success: true,
        data: metrics,
        timestamp: new Date().toISOString()
      });
    } catch (error: any) {
      logger.error('metrics retrieval failed:', error);
      return reply.status(500).send({
        success: false,
        error: error.message
      });
    }
  }
}
