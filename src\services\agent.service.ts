import { Agent, IAgent } from '../models/agent.model';
import { WalletService } from './wallet.service';
import { UserService } from './user.service';
import { AgentStatus, KycStatus, Currency, WalletType, TransactionType } from '../types';
import { logger } from '../config/logger';
import mongoose from 'mongoose';

export class AgentService {
  static async registerAgent(data: {
    userId: string;
    businessName: string;
    businessType: string;
    businessRegistrationNumber?: string;
    taxId?: string;
    location: {
      address: string;
      city: string;
      state: string;
      country: string;
      coordinates?: { latitude: number; longitude: number };
    };
    contactInfo: {
      phone: string;
      email: string;
      whatsapp?: string;
    };
  }): Promise<IAgent> {
    try {
      // check if user already has agent account
      const existingAgent = await Agent.findOne({ user_id: data.userId });
      if (existingAgent) {
        throw new Error('user already has an agent account');
      }

      // get user details
      const user = await UserService.getUserById(data.userId);
      if (!user) {
        throw new Error('user not found');
      }

      // generate unique agent code
      const agentCode = await this.generateAgentCode(data.location.city);

      // create main wallet for agent
      const mainWallet = await WalletService.createWallet({
        userId: data.userId,
        currency: Currency.USD, // default currency
        walletType: WalletType.AGENT,
        isDefault: true
      });

      // create commission wallet
      const commissionWallet = await WalletService.createWallet({
        userId: data.userId,
        currency: Currency.USD,
        walletType: WalletType.COMMISSION,
        isDefault: false
      });

      // create agent record
      const agent = new Agent({
        user_id: data.userId,
        agent_code: agentCode,
        business_name: data.businessName,
        business_type: data.businessType,
        business_registration_number: data.businessRegistrationNumber,
        tax_id: data.taxId,
        location: data.location,
        contact_info: data.contactInfo,
        wallet_info: {
          main_wallet_id: mainWallet._id,
          commission_wallet_id: commissionWallet._id,
          available_balance: 0,
          commission_balance: 0,
          float_balance: 0
        }
      });

      await agent.save();

      logger.info('agent registered successfully', {
        agentId: agent._id,
        agentCode: agent.agent_code,
        userId: data.userId,
        businessName: data.businessName
      });

      return agent;
    } catch (error: any) {
      logger.error('error registering agent:', error);
      throw error;
    }
  }

  static async approveAgent(
    agentId: string,
    approverId: string,
    notes?: string
  ): Promise<boolean> {
    try {
      const agent = await Agent.findById(agentId);
      if (!agent) {
        throw new Error('agent not found');
      }

      if (agent.agent_status !== AgentStatus.PENDING) {
        throw new Error('agent is not in pending status');
      }

      agent.agent_status = AgentStatus.ACTIVE;
      agent.kyc_status = KycStatus.APPROVED;
      agent.approval_data = {
        approved_by: new mongoose.Types.ObjectId(approverId),
        approved_at: new Date(),
        approval_notes: notes || ''
      };

      await agent.save();

      // update user role to agent
      try {
        const user = await UserService.getUserById(agent.user_id.toString());
        if (user) {
          await UserService.updateProfile(agent.user_id.toString(), {});
          // Update role directly in the database since updateProfile doesn't support role
          await mongoose.model('User').findByIdAndUpdate(
            agent.user_id,
            { $set: { role: UserRole.AGENT } }
          );
        }
      } catch (error) {
        logger.error('Failed to update user role to agent:', error);
        // Don't fail the approval if role update fails
      }

      logger.info('agent approved', {
        agentId,
        agentCode: agent.agent_code,
        approverId
      });

      return true;
    } catch (error: any) {
      logger.error('error approving agent:', error);
      throw error;
    }
  }

  static async performCashIn(data: {
    agentId: string;
    customerId: string;
    amount: number;
    currency: Currency;
    description?: string;
  }): Promise<{
    success: boolean;
    transactionId: string;
    commission: number;
    customerBalance: number;
    agentBalance: number;
  }> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const agent = await Agent.findById(data.agentId).session(session);
      if (!agent) {
        throw new Error('agent not found');
      }

      // check if agent can perform transaction
      const canPerform = (agent as any).canPerformTransaction('cash_in', data.amount);
      if (!canPerform.allowed) {
        throw new Error(canPerform.reason);
      }

      // get customer wallet
      const customerWallet = await WalletService.getDefaultWallet(data.customerId, data.currency);
      if (!customerWallet) {
        throw new Error('customer wallet not found');
      }

      // get agent wallet
      const agentWallet = await WalletService.getWalletById(agent.wallet_info.main_wallet_id.toString());
      if (!agentWallet) {
        throw new Error('agent wallet not found');
      }

      // calculate commission
      const commission = (agent as any).calculateCommission('cash_in', data.amount);

      // credit customer wallet
      const customerResult = await WalletService.creditWallet({
        walletId: customerWallet._id.toString(),
        amount: data.amount,
        description: data.description || `Cash in via agent ${agent.agent_code}`,
        transactionType: TransactionType.CASH_IN,
        metadata: {
          agentId: data.agentId,
          agentCode: agent.agent_code,
          commission,
          transactionType: 'cash_in'
        }
      });

      // debit agent wallet (agent gives cash, receives digital money)
      await WalletService.debitWallet({
        walletId: agentWallet._id.toString(),
        amount: data.amount,
        description: `Cash in for customer ${data.customerId}`,
        transactionType: TransactionType.CASH_IN,
        metadata: {
          customerId: data.customerId,
          commission,
          transactionType: 'cash_in'
        }
      });

      // credit agent commission
      const commissionWallet = await WalletService.getWalletById(agent.wallet_info.commission_wallet_id.toString());
      if (commissionWallet) {
        await WalletService.creditWallet({
          walletId: commissionWallet._id.toString(),
          amount: commission,
          description: `Cash in commission - ${agent.agent_code}`,
          transactionType: TransactionType.COMMISSION,
          metadata: {
            relatedTransaction: customerResult.transaction._id,
            transactionType: 'cash_in'
          }
        });
      }

      // update agent statistics
      (agent as any).updateStatistics('cash_in', data.amount, commission);
      agent.wallet_info.available_balance = agentWallet.balance - data.amount;
      agent.wallet_info.commission_balance += commission;
      await agent.save({ session });

      await session.commitTransaction();

      logger.info('cash in transaction completed', {
        agentId: data.agentId,
        customerId: data.customerId,
        amount: data.amount,
        commission,
        transactionId: customerResult.transaction._id
      });

      return {
        success: true,
        transactionId: customerResult.transaction._id.toString(),
        commission,
        customerBalance: customerWallet.balance + data.amount,
        agentBalance: agentWallet.balance - data.amount
      };
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('cash in transaction failed:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async performCashOut(data: {
    agentId: string;
    customerId: string;
    amount: number;
    currency: Currency;
    description?: string;
  }): Promise<{
    success: boolean;
    transactionId: string;
    commission: number;
    customerBalance: number;
    agentBalance: number;
  }> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const agent = await Agent.findById(data.agentId).session(session);
      if (!agent) {
        throw new Error('agent not found');
      }

      // check if agent can perform transaction
      const canPerform = (agent as any).canPerformTransaction('cash_out', data.amount);
      if (!canPerform.allowed) {
        throw new Error(canPerform.reason);
      }

      // get customer wallet
      const customerWallet = await WalletService.getDefaultWallet(data.customerId, data.currency);
      if (!customerWallet) {
        throw new Error('customer wallet not found');
      }

      // check customer balance
      if (customerWallet.available_balance < data.amount) {
        throw new Error('insufficient customer balance');
      }

      // get agent wallet
      const agentWallet = await WalletService.getWalletById(agent.wallet_info.main_wallet_id.toString());
      if (!agentWallet) {
        throw new Error('agent wallet not found');
      }

      // calculate commission
      const commission = (agent as any).calculateCommission('cash_out', data.amount);

      // debit customer wallet
      const customerResult = await WalletService.debitWallet({
        walletId: customerWallet._id.toString(),
        amount: data.amount,
        description: data.description || `Cash out via agent ${agent.agent_code}`,
        transactionType: TransactionType.CASH_OUT,
        metadata: {
          agentId: data.agentId,
          agentCode: agent.agent_code,
          commission,
          transactionType: 'cash_out'
        }
      });

      // credit agent wallet (agent receives digital money, gives cash)
      await WalletService.creditWallet({
        walletId: agentWallet._id.toString(),
        amount: data.amount,
        description: `Cash out for customer ${data.customerId}`,
        transactionType: TransactionType.CASH_OUT,
        metadata: {
          customerId: data.customerId,
          commission,
          transactionType: 'cash_out'
        }
      });

      // credit agent commission
      const commissionWallet = await WalletService.getWalletById(agent.wallet_info.commission_wallet_id.toString());
      if (commissionWallet) {
        await WalletService.creditWallet({
          walletId: commissionWallet._id.toString(),
          amount: commission,
          description: `Cash out commission - ${agent.agent_code}`,
          transactionType: TransactionType.COMMISSION,
          metadata: {
            relatedTransaction: customerResult.transaction._id,
            transactionType: 'cash_out'
          }
        });
      }

      // update agent statistics
      (agent as any).updateStatistics('cash_out', data.amount, commission);
      agent.wallet_info.available_balance = agentWallet.balance + data.amount;
      agent.wallet_info.commission_balance += commission;
      await agent.save({ session });

      await session.commitTransaction();

      logger.info('cash out transaction completed', {
        agentId: data.agentId,
        customerId: data.customerId,
        amount: data.amount,
        commission,
        transactionId: customerResult.transaction._id
      });

      return {
        success: true,
        transactionId: customerResult.transaction._id.toString(),
        commission,
        customerBalance: customerWallet.balance - data.amount,
        agentBalance: agentWallet.balance + data.amount
      };
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('cash out transaction failed:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async getAgentById(agentId: string): Promise<IAgent | null> {
    try {
      if (!mongoose.Types.ObjectId.isValid(agentId)) {
        return null;
      }

      const agent = await Agent.findById(agentId)
        .populate('user_id', 'first_name last_name email phone')
        .populate('parent_agent', 'agent_code business_name')
        .populate('sub_agents', 'agent_code business_name agent_status');

      return agent;
    } catch (error: any) {
      logger.error('error getting agent by id:', error);
      return null;
    }
  }

  static async getAgentByCode(agentCode: string): Promise<IAgent | null> {
    try {
      const agent = await Agent.findOne({ agent_code: agentCode.toUpperCase() })
        .populate('user_id', 'first_name last_name email phone');

      return agent;
    } catch (error: any) {
      logger.error('error getting agent by code:', error);
      return null;
    }
  }

  static async searchAgents(query: {
    search?: string;
    status?: AgentStatus;
    kycStatus?: KycStatus;
    city?: string;
    state?: string;
    businessType?: string;
    page?: number;
    limit?: number;
  }): Promise<{
    agents: IAgent[];
    total: number;
    page: number;
    pages: number;
  }> {
    try {
      const page = query.page || 1;
      const limit = Math.min(query.limit || 20, 100);
      const skip = (page - 1) * limit;

      const filter: any = {};

      if (query.search) {
        filter.$or = [
          { agent_code: { $regex: query.search, $options: 'i' } },
          { business_name: { $regex: query.search, $options: 'i' } },
          { 'contact_info.email': { $regex: query.search, $options: 'i' } },
          { 'contact_info.phone': { $regex: query.search, $options: 'i' } }
        ];
      }

      if (query.status) filter.agent_status = query.status;
      if (query.kycStatus) filter.kyc_status = query.kycStatus;
      if (query.city) filter['location.city'] = query.city;
      if (query.state) filter['location.state'] = query.state;
      if (query.businessType) filter.business_type = query.businessType;

      const [agents, total] = await Promise.all([
        Agent.find(filter)
          .sort({ created_at: -1 })
          .skip(skip)
          .limit(limit)
          .populate('user_id', 'first_name last_name email')
          .lean(),
        Agent.countDocuments(filter)
      ]);

      return {
        agents: agents as IAgent[],
        total,
        page,
        pages: Math.ceil(total / limit)
      };
    } catch (error: any) {
      logger.error('error searching agents:', error);
      throw error;
    }
  }

  private static async generateAgentCode(city: string): Promise<string> {
    try {
      const cityPrefix = city.substring(0, 3).toUpperCase();
      let agentCode: string;
      let isUnique = false;
      let attempts = 0;

      do {
        const randomNum = Math.floor(Math.random() * 9000) + 1000;
        agentCode = `${cityPrefix}${randomNum}`;
        
        const existing = await Agent.findOne({ agent_code: agentCode });
        isUnique = !existing;
        attempts++;
      } while (!isUnique && attempts < 10);

      if (!isUnique) {
        // fallback to timestamp-based code
        agentCode = `AGT${Date.now().toString().slice(-6)}`;
      }

      return agentCode;
    } catch (error: any) {
      logger.error('error generating agent code:', error);
      return `AGT${Date.now().toString().slice(-6)}`;
    }
  }

  static async suspendAgent(
    agentId: string,
    suspendedBy: string,
    reason: string,
    notes?: string
  ): Promise<boolean> {
    try {
      const agent = await Agent.findByIdAndUpdate(
        agentId,
        {
          $set: {
            agent_status: AgentStatus.SUSPENDED,
            suspension_data: {
              suspended_by: new mongoose.Types.ObjectId(suspendedBy),
              suspended_at: new Date(),
              suspension_reason: reason,
              suspension_notes: notes || ''
            }
          }
        },
        { new: true }
      );

      if (agent) {
        logger.info('agent suspended', {
          agentId,
          agentCode: agent.agent_code,
          suspendedBy,
          reason
        });
        return true;
      }

      return false;
    } catch (error: any) {
      logger.error('error suspending agent:', error);
      return false;
    }
  }

  static async reactivateAgent(agentId: string): Promise<boolean> {
    try {
      const agent = await Agent.findByIdAndUpdate(
        agentId,
        {
          $set: { agent_status: AgentStatus.ACTIVE },
          $unset: { suspension_data: 1 }
        },
        { new: true }
      );

      if (agent) {
        logger.info('agent reactivated', {
          agentId,
          agentCode: agent.agent_code
        });
        return true;
      }

      return false;
    } catch (error: any) {
      logger.error('error reactivating agent:', error);
      return false;
    }
  }
}
