import { FastifyRequest, FastifyReply } from 'fastify';
import { AuthenticatedRequest } from '../types';
import { TransferService } from '../services/transfer.service';
import { ValidationService } from '../services/validation.service';
import { ExternalApiService } from '../services/external-api.service';
import { logger } from '../config/logger';

export const sendMoney = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required',
      error_code: 'AUTH_REQUIRED'
    });
  }

  try {
    const validation = await ValidationService.validateData(request.body, ValidationService.transferSchema);
    
    if (!validation.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'validation failed',
        errors: validation.errors,
        error_code: 'VALIDATION_ERROR'
      });
    }

    const result = await TransferService.sendMoney({
      senderId: request.user.id,
      ...validation.data
    });

    return reply.status(200).send({
      success: true,
      message: 'money sent successfully',
      data: {
        transferDetails: {
          transferId: result.transferId,
          transactionRef: result.transactionRef,
          amount: result.amount,
          fee: result.fee,
          totalAmount: result.totalAmount,
          currency: validation.data.currency,
          status: result.status,
          createdAt: new Date().toISOString(),
          estimatedDelivery: result.estimatedDelivery,
          transferType: "p2p"
        },
        recipientInfo: {
          recipientId: result.recipient.id,
          recipientName: result.recipient.name,
          recipientEmail: result.recipient.email,
          recipientPhone: result.recipient.phone,
          isRegistered: true
        },
        senderInfo: {
          senderId: request.user.id
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        timestamp: new Date().toISOString(),
        processingTimeMs: Date.now() - ((request as any).startTime || Date.now()),
        apiVersion: "v1"
      }
    });
  } catch (error: any) {
    logger.error('send money error:', error);
    
    const errorMessages: Record<string, string> = {
      'sender not found': 'sender account not found',
      'recipient not found': 'recipient not found or not registered',
      'insufficient balance': 'insufficient wallet balance',
      'sender wallet not found': 'sender wallet not available',
      'recipient wallet not found': 'recipient wallet not available'
    };

    const message = errorMessages[error.message] || 'transfer failed';
    
    return reply.status(400).send({
      success: false,
      message,
      error_code: 'TRANSFER_FAILED',
      error_details: {
        reason: error.message,
        timestamp: new Date().toISOString()
      }
    });
  }
};

export const sendRemittance = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user authentication required'
    });
  }

  try {
    // TODO: add remittance validation schema
    const data = request.body as any;

    const result = await TransferService.sendRemittance({
      senderId: request.user.id,
      ...data
    });

    return reply.status(200).send({
      success: true,
      message: 'remittance initiated successfully',
      data: {
        remittanceTransaction: {
          remittanceId: result.remittanceId,
          referenceNumber: result.referenceNumber,
          trackingCode: result.trackingCode,
          sourceAmount: result.sourceAmount,
          targetAmount: result.targetAmount,
          exchangeRate: result.exchangeRate,
          processingFee: result.fee,
          totalCost: result.totalCost,
          estimatedDelivery: result.estimatedDelivery,
          currentStatus: 'processing',
          initiatedAt: new Date().toISOString(),
          remittanceType: "international"
        },
        recipientDetails: {
          recipientName: result.recipient.name,
          recipientPhone: result.recipient.phone,
          recipientCountry: result.recipient.country,
          deliveryMethod: result.recipient.deliveryMethod
        },
        exchangeDetails: {
          sourceCurrency: data.sourceCurrency,
          targetCurrency: data.targetCurrency,
          currentRate: result.exchangeRate,
          rateTimestamp: new Date().toISOString(),
          rateProvider: "central_bank"
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        processingTimeMs: Date.now() - ((request as any).startTime || Date.now()),
        apiVersion: "v1",
        serviceType: "remittance"
      }
    });
  } catch (error: any) {
    logger.error('remittance error:', error);
    
    return reply.status(400).send({
      success: false,
      message: 'remittance failed',
      error: error.message,
      error_code: 'REMITTANCE_FAILED'
    });
  }
};

export const payBill = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const data = request.body as any;

    const result = await TransferService.payBill({
      userId: request.user.id,
      ...data
    });

    return reply.status(200).send({
      success: true,
      message: 'bill payment successful',
      data: {
        paymentTransaction: {
          paymentId: result.paymentId,
          paymentReference: result.referenceNumber,
          billAmount: result.amount,
          serviceFee: result.fee,
          totalAmountPaid: result.totalAmount,
          paymentStatus: result.status,
          confirmationCode: result.confirmationCode,
          processedAt: new Date().toISOString(),
          paymentMethod: "wallet"
        },
        billDetails: {
          providerName: result.provider,
          customerAccount: result.accountNumber,
          billCategory: data.billType
        },
        customerInfo: {
          customerId: request.user.id
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        processedAt: new Date().toISOString(),
        serviceType: "bill_payment"
      }
    });
  } catch (error: any) {
    logger.error('bill payment error:', error);
    
    return reply.status(400).send({
      success: false,
      message: 'bill payment failed',
      error: error.message,
      error_code: 'BILL_PAYMENT_FAILED'
    });
  }
};

export const getTransferHistory = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { page = 1, limit = 20, type, status } = request.query as any;

    // TODO: get actual transfer history
    const transfers = [
      {
        id: "txn-abc123",
        reference: "TXN_1234567890_ABC",
        type: "p2p_transfer",
        amount: 50.00,
        fee: 0.50,
        currency: "USD",
        status: "completed",
        recipient: {
          name: "John Doe",
          email: "<EMAIL>"
        },
        created_at: "2024-01-15T10:30:00Z",
        completed_at: "2024-01-15T10:30:05Z"
      }
    ];

    return reply.status(200).send({
      success: true,
      message: 'transfer history retrieved',
      data: {
        transfers,
        pagination: {
          current_page: parseInt(page),
          per_page: parseInt(limit),
          total_items: 1,
          total_pages: 1,
          has_next: false,
          has_previous: false
        }
      },
      meta: {
        request_id: (request as any).requestId,
        filters_applied: {
          type: type || null,
          status: status || null
        }
      }
    });
  } catch (error: any) {
    logger.error('get transfer history error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve transfer history'
    });
  }
};

export const getExchangeRates = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const { from, to } = request.query as any;

    const ratesData = await ExternalApiService.getExchangeRates(from, to);

    const result = ratesData.rates;

    return reply.status(200).send({
      success: true,
      message: 'exchange rates retrieved',
      data: {
        rates: result,
        last_updated: ratesData.timestamp,
        source: ratesData.source
      },
      meta: {
        request_id: (request as any).requestId,
        cache_ttl: 300,
        api_version: "v1"
      }
    });
  } catch (error: any) {
    logger.error('get exchange rates error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to get exchange rates'
    });
  }
};

export const getBillProviders = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const { country, bill_type } = request.query as any;

    const providersData = await ExternalApiService.getBillProviders(country, bill_type);
    const filteredProviders = providersData.providers;

    return reply.status(200).send({
      success: true,
      message: 'bill providers retrieved',
      data: {
        providers: filteredProviders,
        total_count: filteredProviders.length
      },
      meta: {
        filters: {
          country: country || null,
          bill_type: bill_type || null
        }
      }
    });
  } catch (error: any) {
    logger.error('get bill providers error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to get bill providers'
    });
  }
};
