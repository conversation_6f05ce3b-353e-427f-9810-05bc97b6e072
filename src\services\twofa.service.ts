import speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import { logger, securityLogger } from '../config/logger';
import { CryptoUtils } from '../utils/crypto';
import { redis } from '../config/redis';

export interface TwoFASetup {
  secret: string;
  qrCodeUrl: string;
  backupCodes: string[];
}

export interface TwoFAVerification {
  isValid: boolean;
  usedBackupCode?: boolean;
}

export class TwoFAService {
  private static readonly APP_NAME = 'AeTrust';
  private static readonly BACKUP_CODES_COUNT = 10;
  private static readonly OTP_WINDOW = 2;
  private static readonly RATE_LIMIT_WINDOW = 300;
  private static readonly MAX_ATTEMPTS = 5;

  static async generateSetup(userId: string, userEmail: string): Promise<TwoFASetup> {
    try {
      const secret = speakeasy.generateSecret({
        name: `${this.APP_NAME} (${userEmail})`,
        issuer: this.APP_NAME,
        length: 32
      });

      const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url!);
      const backupCodes = this.generateBackupCodes();

      await this.storeUserSecret(userId, secret.base32, backupCodes);

      securityLogger.info('2FA setup generated', {
        userId,
        email: userEmail
      });

      return {
        secret: secret.base32,
        qrCodeUrl,
        backupCodes
      };
    } catch (error) {
      logger.error('Error generating 2FA setup:', error);
      throw new Error('failed to generate 2FA setup');
    }
  }

  /**
   * Verify 2FA token
   */
  static async verifyToken(
    userId: string,
    token: string,
    allowBackupCode: boolean = true
  ): Promise<TwoFAVerification> {
    try {
      // Check rate limiting
      const isRateLimited = await this.checkRateLimit(userId);
      if (isRateLimited) {
        securityLogger.warn('2FA verification rate limited', { userId });
        throw new Error('Too many verification attempts. Please try again later.');
      }

      // Get user's secret and backup codes
      const userData = await this.getUserSecret(userId);
      if (!userData) {
        throw new Error('2FA not set up for this user');
      }

      // First try TOTP verification
      const totpValid = speakeasy.totp.verify({
        secret: userData.secret,
        encoding: 'base32',
        token,
        window: this.OTP_WINDOW
      });

      if (totpValid) {
        await this.clearRateLimit(userId);
        securityLogger.info('2FA TOTP verification successful', { userId });
        return { isValid: true };
      }

      // If TOTP fails and backup codes are allowed, try backup code
      if (allowBackupCode && userData.backupCodes.includes(token)) {
        // Remove used backup code
        await this.removeBackupCode(userId, token);
        await this.clearRateLimit(userId);
        
        securityLogger.info('2FA backup code verification successful', { 
          userId,
          remainingCodes: userData.backupCodes.length - 1
        });
        
        return { isValid: true, usedBackupCode: true };
      }

      // Increment failed attempts
      await this.incrementFailedAttempts(userId);
      
      securityLogger.warn('2FA verification failed', { 
        userId,
        tokenLength: token.length
      });

      return { isValid: false };
    } catch (error) {
      logger.error('Error verifying 2FA token:', error);
      throw error;
    }
  }

  /**
   * Disable 2FA for a user
   */
  static async disable2FA(userId: string): Promise<void> {
    try {
      await redis.del(`2fa:${userId}`);
      await this.clearRateLimit(userId);
      
      securityLogger.info('2FA disabled', { userId });
    } catch (error) {
      logger.error('Error disabling 2FA:', error);
      throw new Error('Failed to disable 2FA');
    }
  }

  /**
   * Check if user has 2FA enabled
   */
  static async is2FAEnabled(userId: string): Promise<boolean> {
    try {
      const userData = await this.getUserSecret(userId);
      return userData !== null;
    } catch (error) {
      logger.error('Error checking 2FA status:', error);
      return false;
    }
  }

  /**
   * Generate new backup codes
   */
  static async generateNewBackupCodes(userId: string): Promise<string[]> {
    try {
      const userData = await this.getUserSecret(userId);
      if (!userData) {
        throw new Error('2FA not set up for this user');
      }

      const newBackupCodes = this.generateBackupCodes();
      await this.storeUserSecret(userId, userData.secret, newBackupCodes);

      securityLogger.info('New backup codes generated', { userId });
      return newBackupCodes;
    } catch (error) {
      logger.error('Error generating new backup codes:', error);
      throw new Error('Failed to generate new backup codes');
    }
  }

  /**
   * Get remaining backup codes count
   */
  static async getRemainingBackupCodes(userId: string): Promise<number> {
    try {
      const userData = await this.getUserSecret(userId);
      return userData ? userData.backupCodes.length : 0;
    } catch (error) {
      logger.error('Error getting backup codes count:', error);
      return 0;
    }
  }

  /**
   * Generate backup codes
   */
  private static generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < this.BACKUP_CODES_COUNT; i++) {
      codes.push(CryptoUtils.generateRandomString(8).toUpperCase());
    }
    return codes;
  }

  /**
   * Store user's 2FA secret and backup codes
   */
  private static async storeUserSecret(
    userId: string,
    secret: string,
    backupCodes: string[]
  ): Promise<void> {
    const data = {
      secret: CryptoUtils.encrypt(secret),
      backupCodes: backupCodes.map(code => CryptoUtils.encrypt(code)),
      createdAt: new Date().toISOString()
    };

    await redis.set(`2fa:${userId}`, JSON.stringify(data));
  }

  /**
   * Get user's 2FA secret and backup codes
   */
  private static async getUserSecret(userId: string): Promise<{
    secret: string;
    backupCodes: string[];
  } | null> {
    const data = await redis.get(`2fa:${userId}`);
    if (!data) {
      return null;
    }

    const parsed = JSON.parse(data);
    return {
      secret: CryptoUtils.decrypt(parsed.secret),
      backupCodes: parsed.backupCodes.map((code: string) => CryptoUtils.decrypt(code))
    };
  }

  /**
   * Remove used backup code
   */
  private static async removeBackupCode(userId: string, usedCode: string): Promise<void> {
    const userData = await this.getUserSecret(userId);
    if (!userData) {
      return;
    }

    const updatedCodes = userData.backupCodes.filter(code => code !== usedCode);
    await this.storeUserSecret(userId, userData.secret, updatedCodes);
  }

  /**
   * Check rate limiting for 2FA attempts
   */
  private static async checkRateLimit(userId: string): Promise<boolean> {
    const key = `2fa_attempts:${userId}`;
    const attempts = await redis.get(key);
    return attempts ? parseInt(attempts) >= this.MAX_ATTEMPTS : false;
  }

  /**
   * Increment failed attempts counter
   */
  private static async incrementFailedAttempts(userId: string): Promise<void> {
    const key = `2fa_attempts:${userId}`;
    const current = await redis.get(key);
    const attempts = current ? parseInt(current) + 1 : 1;
    
    await redis.set(key, attempts.toString(), this.RATE_LIMIT_WINDOW);
  }

  /**
   * Clear rate limiting
   */
  private static async clearRateLimit(userId: string): Promise<void> {
    await redis.del(`2fa_attempts:${userId}`);
  }
}
