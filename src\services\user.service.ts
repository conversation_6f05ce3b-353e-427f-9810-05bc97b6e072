import { User, IUser } from '../models/user.model';
import { CryptoUtils } from '../utils/crypto';
import { logger, securityLogger } from '../config/logger';
import { UserRole, KycStatus, AccountStatus } from '../types';
import mongoose from 'mongoose';

export class UserService {
  static async createUser(userData: {
    email: string;
    password: string;
    phone: string;
    firstName: string;
    lastName: string;
    dateOfBirth: Date;
    role?: UserRole;
  }): Promise<IUser> {
    try {
      // check if user already exists
      const existingUser = await User.findOne({
        $or: [
          { email: userData.email },
          { phone: userData.phone }
        ]
      });

      if (existingUser) {
        throw new Error('user already exists with this email or phone');
      }

      const hashedPassword = await CryptoUtils.hashPassword(userData.password);

      const user = new User({
        email: userData.email,
        password: hashedPassword,
        phone: userData.phone,
        first_name: userData.firstName,
        last_name: userData.lastName,
        date_of_birth: userData.dateOfBirth,
        role: userData.role || UserRole.USER,
        security: {
          email_verification_token: CryptoUtils.generateSecureToken(),
          email_verification_expires: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
        }
      });

      await user.save();

      logger.info('user created successfully', {
        userId: user._id,
        email: userData.email,
        role: user.role
      });

      return user;
    } catch (error: any) {
      logger.error('error creating user:', error);
      throw error;
    }
  }

  static async getUserById(userId: string): Promise<IUser | null> {
    try {
      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return null;
      }

      const user = await User.findOne({
        _id: userId,
        deleted_at: null,
        account_status: { $ne: AccountStatus.CLOSED }
      });

      return user;
    } catch (error: any) {
      logger.error('error getting user by id:', error);
      return null;
    }
  }

  static async getUserByEmail(email: string): Promise<IUser | null> {
    try {
      const user = await User.findOne({
        email: email.toLowerCase(),
        deleted_at: null,
        account_status: { $ne: AccountStatus.CLOSED }
      });

      return user;
    } catch (error: any) {
      logger.error('error getting user by email:', error);
      return null;
    }
  }

  static async getUserByPhone(phone: string): Promise<IUser | null> {
    try {
      const user = await User.findOne({
        phone,
        deleted_at: null,
        account_status: { $ne: AccountStatus.CLOSED }
      });

      return user;
    } catch (error: any) {
      logger.error('error getting user by phone:', error);
      return null;
    }
  }

  static async updateProfile(userId: string, updateData: {
    firstName?: string;
    lastName?: string;
    phone?: string;
    bio?: string;
    address?: any;
  }): Promise<IUser | null> {
    try {
      const updateFields: any = {};

      if (updateData.firstName) updateFields.first_name = updateData.firstName;
      if (updateData.lastName) updateFields.last_name = updateData.lastName;
      if (updateData.phone) updateFields.phone = updateData.phone;
      if (updateData.bio !== undefined) updateFields.bio = updateData.bio;
      if (updateData.address) updateFields.address = updateData.address;

      const user = await User.findOneAndUpdate(
        { _id: userId, deleted_at: null },
        { $set: updateFields },
        { new: true, runValidators: true }
      );

      if (user) {
        logger.info('user profile updated', {
          userId,
          updatedFields: Object.keys(updateFields)
        });
      }

      return user;
    } catch (error: any) {
      logger.error('error updating user profile:', error);
      throw error;
    }
  }

  static async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<boolean> {
    try {
      const user = await User.findOne({ _id: userId, deleted_at: null }).select('+password');
      
      if (!user) {
        throw new Error('user not found');
      }

      const isCurrentPasswordValid = await CryptoUtils.verifyPassword(currentPassword, user.password);
      
      if (!isCurrentPasswordValid) {
        securityLogger.warn('password change failed - invalid current password', {
          userId,
          ip: 'unknown' // would be passed from controller
        });
        throw new Error('current password is incorrect');
      }

      const hashedNewPassword = await CryptoUtils.hashPassword(newPassword);
      
      await User.updateOne(
        { _id: userId },
        { 
          $set: { 
            password: hashedNewPassword,
            'security.password_reset_token': null,
            'security.password_reset_expires': null
          }
        }
      );

      securityLogger.info('password changed successfully', { userId });
      return true;
    } catch (error: any) {
      logger.error('error changing password:', error);
      throw error;
    }
  }

  static async verifyEmail(token: string): Promise<boolean> {
    try {
      const user = await User.findOne({
        'security.email_verification_token': token,
        'security.email_verification_expires': { $gt: new Date() },
        deleted_at: null
      });

      if (!user) {
        return false;
      }

      await User.updateOne(
        { _id: user._id },
        {
          $set: {
            is_verified: true,
            'security.email_verification_token': null,
            'security.email_verification_expires': null
          }
        }
      );

      securityLogger.info('email verified successfully', {
        userId: user._id,
        email: user.email
      });

      return true;
    } catch (error: any) {
      logger.error('error verifying email:', error);
      return false;
    }
  }

  static async updateKycStatus(userId: string, status: KycStatus, reason?: string): Promise<boolean> {
    try {
      const user = await User.findOneAndUpdate(
        { _id: userId, deleted_at: null },
        { $set: { kyc_status: status } },
        { new: true }
      );

      if (user) {
        securityLogger.info('kyc status updated', {
          userId,
          oldStatus: user.kyc_status,
          newStatus: status,
          reason
        });
        return true;
      }

      return false;
    } catch (error: any) {
      logger.error('error updating kyc status:', error);
      return false;
    }
  }

  static async searchUsers(query: {
    search?: string;
    role?: UserRole;
    kycStatus?: KycStatus;
    accountStatus?: AccountStatus;
    page?: number;
    limit?: number;
  }): Promise<{ users: IUser[]; total: number; page: number; pages: number }> {
    try {
      const page = query.page || 1;
      const limit = Math.min(query.limit || 20, 100);
      const skip = (page - 1) * limit;

      const filter: any = { deleted_at: null };

      if (query.search) {
        filter.$or = [
          { first_name: { $regex: query.search, $options: 'i' } },
          { last_name: { $regex: query.search, $options: 'i' } },
          { email: { $regex: query.search, $options: 'i' } },
          { phone: { $regex: query.search, $options: 'i' } }
        ];
      }

      if (query.role) filter.role = query.role;
      if (query.kycStatus) filter.kyc_status = query.kycStatus;
      if (query.accountStatus) filter.account_status = query.accountStatus;

      const [users, total] = await Promise.all([
        User.find(filter)
          .sort({ created_at: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        User.countDocuments(filter)
      ]);

      return {
        users: users as IUser[],
        total,
        page,
        pages: Math.ceil(total / limit)
      };
    } catch (error: any) {
      logger.error('error searching users:', error);
      throw error;
    }
  }

  static async updateWalletBalance(userId: string, amount: number, operation: 'add' | 'subtract'): Promise<boolean> {
    try {
      const updateOperation = operation === 'add' ? { $inc: { wallet_balance: amount } } : { $inc: { wallet_balance: -amount } };

      const user = await User.findOneAndUpdate(
        { 
          _id: userId, 
          deleted_at: null,
          ...(operation === 'subtract' && { wallet_balance: { $gte: amount } })
        },
        updateOperation,
        { new: true }
      );

      if (!user) {
        throw new Error(operation === 'subtract' ? 'insufficient balance' : 'user not found');
      }

      logger.info('wallet balance updated', {
        userId,
        operation,
        amount,
        newBalance: user.wallet_balance
      });

      return true;
    } catch (error: any) {
      logger.error('error updating wallet balance:', error);
      throw error;
    }
  }

  static async deleteAccount(userId: string): Promise<boolean> {
    try {
      // first get the user to access email and phone
      const existingUser = await User.findOne({ _id: userId, deleted_at: null });

      if (!existingUser) {
        return false;
      }

      const user = await User.findOneAndUpdate(
        { _id: userId, deleted_at: null },
        {
          $set: {
            deleted_at: new Date(),
            account_status: AccountStatus.CLOSED,
            email: `deleted_${Date.now()}_${existingUser.email}`,
            phone: `deleted_${Date.now()}_${existingUser.phone}`
          }
        },
        { new: true }
      );

      if (user) {
        securityLogger.info('user account deleted', {
          userId,
          email: user.email
        });
        return true;
      }

      return false;
    } catch (error: any) {
      logger.error('error deleting account:', error);
      return false;
    }
  }
}
