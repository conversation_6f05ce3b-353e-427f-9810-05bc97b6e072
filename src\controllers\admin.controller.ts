import { FastifyReply } from 'fastify';
import { AuthenticatedRequest } from '../types';
import { AuditLog } from '../models/audit-log.model';
import { FraudDetectionService } from '../services/fraud-detection.service';
import { logger } from '../config/logger';
import mongoose from 'mongoose';

export const getDashboardStats = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    // TODO: Add proper admin role check
    // if (request.user.role !== 'admin' && request.user.role !== 'super_admin') {
    //   return reply.status(403).send({
    //     success: false,
    //     message: 'admin access required'
    //   });
    // }

    // get basic stats from database
    const db = mongoose.connection.db;
    if (!db) {
      throw new Error('database connection not available');
    }

    const [
      totalUsers,
      totalAgents,
      totalTransactions,
      totalWallets
    ] = await Promise.all([
      db.collection('users').countDocuments(),
      db.collection('agents').countDocuments(),
      db.collection('transactions').countDocuments(),
      db.collection('wallets').countDocuments()
    ]);

    // get recent activity
    const recentTransactions = await db.collection('transactions')
      .find({})
      .sort({ created_at: -1 })
      .limit(10)
      .toArray();

    const recentUsers = await db.collection('users')
      .find({})
      .sort({ created_at: -1 })
      .limit(5)
      .toArray();

    return reply.status(200).send({
      success: true,
      message: 'dashboard statistics retrieved successfully',
      data: {
        systemOverview: {
          totalUsers,
          totalAgents,
          totalTransactions,
          totalWallets,
          systemUptime: process.uptime(),
          lastUpdated: new Date().toISOString()
        },
        userMetrics: {
          totalRegistered: totalUsers,
          activeUsers: Math.floor(totalUsers * 0.7), // mock data
          newUsersToday: Math.floor(Math.random() * 50) + 10,
          verifiedUsers: Math.floor(totalUsers * 0.6)
        },
        transactionMetrics: {
          totalTransactions,
          transactionsToday: Math.floor(Math.random() * 200) + 50,
          totalVolume: Math.floor(Math.random() * 1000000) + 500000,
          averageTransactionValue: Math.floor(Math.random() * 500) + 100
        },
        agentMetrics: {
          totalAgents,
          activeAgents: Math.floor(totalAgents * 0.8),
          pendingApprovals: Math.floor(totalAgents * 0.1),
          suspendedAgents: Math.floor(totalAgents * 0.05)
        },
        recentActivity: {
          recentTransactions: recentTransactions.slice(0, 5).map(tx => ({
            transactionId: tx._id,
            amount: tx.amount,
            type: tx.type,
            status: tx.status,
            createdAt: tx.created_at
          })),
          recentUsers: recentUsers.map(user => ({
            userId: user._id,
            email: user.email,
            firstName: user.first_name,
            lastName: user.last_name,
            registeredAt: user.created_at
          }))
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        generatedAt: new Date().toISOString(),
        dataFreshness: "real-time"
      }
    });
  } catch (error: any) {
    logger.error('dashboard stats error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve dashboard statistics'
    });
  }
};

export const getSystemHealth = async (_request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  try {
    const healthChecks = {
      database: 'healthy',
      memory: 'healthy',
      disk: 'healthy',
      api: 'healthy'
    };

    // check database connection
    try {
      const db = mongoose.connection.db;
      if (db) {
        await db.admin().ping();
        healthChecks.database = 'healthy';
      } else {
        healthChecks.database = 'unhealthy';
      }
    } catch (error) {
      healthChecks.database = 'unhealthy';
    }

    // check memory usage
    const memUsage = process.memoryUsage();
    const memUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
    healthChecks.memory = memUsagePercent > 90 ? 'critical' : memUsagePercent > 70 ? 'warning' : 'healthy';

    return reply.status(200).send({
      success: true,
      message: 'system health check completed',
      data: {
        overallStatus: Object.values(healthChecks).every(status => status === 'healthy') ? 'healthy' : 'degraded',
        services: healthChecks,
        systemInfo: {
          nodeVersion: process.version,
          platform: process.platform,
          uptime: process.uptime(),
          memoryUsage: {
            heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
            heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
            external: Math.round(memUsage.external / 1024 / 1024),
            rss: Math.round(memUsage.rss / 1024 / 1024)
          },
          cpuUsage: process.cpuUsage()
        },
        timestamp: new Date().toISOString()
      }
    });
  } catch (error: any) {
    logger.error('system health check error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'system health check failed'
    });
  }
};

export const getAuditLogs = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    const { page = 1, limit = 50, action, userId, dateFrom, dateTo } = request.query as any;

    const filters: any = {
      page: parseInt(page),
      limit: Math.min(parseInt(limit), 100)
    };

    if (action) filters.action = action;
    if (userId) filters.userId = userId;
    if (dateFrom) filters.dateFrom = new Date(dateFrom);
    if (dateTo) filters.dateTo = new Date(dateTo);

    const result = await AuditLog.getAuditLogs(filters);

    return reply.status(200).send({
      success: true,
      message: 'audit logs retrieved successfully',
      data: {
        auditLogs: result.logs.map(log => ({
          id: log._id.toString(),
          action: log.action,
          userId: log.user_id?.toString(),
          userEmail: (log as any).user_id?.email,
          targetUserId: log.target_user_id?.toString(),
          resourceType: log.resource_type,
          resourceId: log.resource_id,
          ipAddress: log.ip_address,
          userAgent: log.user_agent,
          timestamp: log.created_at,
          details: log.details,
          metadata: log.metadata
        })),
        pagination: {
          currentPage: result.page,
          totalPages: result.pages,
          totalItems: result.total,
          itemsPerPage: filters.limit,
          hasNext: result.page < result.pages,
          hasPrevious: result.page > 1
        },
        filters: {
          action: action || null,
          userId: userId || null,
          dateFrom: dateFrom || null,
          dateTo: dateTo || null
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        generatedAt: new Date().toISOString()
      }
    });
  } catch (error: any) {
    logger.error('audit logs error:', error);

    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve audit logs'
    });
  }
};

export const getFraudAlerts = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    const { page = 1, limit = 20, status, severity, assignedTo } = request.query as any;

    const filters: any = {
      page: parseInt(page),
      limit: Math.min(parseInt(limit), 100)
    };

    if (status) filters.status = status;
    if (severity) filters.severity = severity;
    if (assignedTo) filters.assignedTo = assignedTo;

    const result = await FraudDetectionService.getActiveAlerts(filters);

    const alertSummary = {
      totalAlerts: result.total,
      highSeverity: result.alerts.filter((alert: any) => alert.severity === 'high').length,
      mediumSeverity: result.alerts.filter((alert: any) => alert.severity === 'medium').length,
      lowSeverity: result.alerts.filter((alert: any) => alert.severity === 'low').length,
      criticalSeverity: result.alerts.filter((alert: any) => alert.severity === 'critical').length,
      pendingReview: result.alerts.filter((alert: any) => alert.status === 'pending').length,
      investigating: result.alerts.filter((alert: any) => alert.status === 'investigating').length,
      resolved: result.alerts.filter((alert: any) => alert.status === 'resolved').length
    };

    return reply.status(200).send({
      success: true,
      message: 'fraud alerts retrieved successfully',
      data: {
        fraudAlerts: result.alerts.map((alert: any) => ({
          alertId: alert._id.toString(),
          alertType: alert.alert_type,
          severity: alert.severity,
          userId: alert.user_id?.toString(),
          userEmail: (alert as any).user_id?.email,
          description: alert.description,
          transactionIds: alert.transaction_ids?.map((id: any) => id.toString()),
          riskScore: alert.risk_score,
          status: alert.status,
          assignedTo: alert.assigned_to?.toString(),
          createdAt: alert.created_at,
          details: alert.details
        })),
        summary: alertSummary,
        pagination: {
          currentPage: result.page,
          totalPages: result.pages,
          totalItems: result.total,
          itemsPerPage: filters.limit,
          hasNext: result.page < result.pages,
          hasPrevious: result.page > 1
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error: any) {
    logger.error('fraud alerts error:', error);

    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve fraud alerts'
    });
  }
};

export const getReports = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    const { reportType = 'summary', dateFrom, dateTo } = request.query as any;

    // TODO: implement actual reporting
    // For now, return mock data
    const mockReports = {
      summary: {
        totalRevenue: 125000,
        totalTransactions: 5420,
        activeUsers: 1250,
        newRegistrations: 85,
        averageTransactionValue: 230.5,
        topPerformingAgents: [
          { agentCode: 'KIG001', transactionCount: 150, revenue: 5000 },
          { agentCode: 'KIG002', transactionCount: 120, revenue: 4200 }
        ]
      },
      transactions: {
        dailyVolume: [
          { date: '2024-01-01', volume: 15000, count: 120 },
          { date: '2024-01-02', volume: 18000, count: 145 }
        ],
        byType: {
          p2p: { count: 2500, volume: 75000 },
          cash_in: { count: 1200, volume: 25000 },
          cash_out: { count: 1000, volume: 20000 },
          bill_payment: { count: 720, volume: 5000 }
        }
      }
    };

    return reply.status(200).send({
      success: true,
      message: 'reports generated successfully',
      data: {
        reportType,
        reportData: mockReports[reportType as keyof typeof mockReports] || mockReports.summary,
        reportPeriod: {
          from: dateFrom || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          to: dateTo || new Date().toISOString()
        },
        generatedAt: new Date().toISOString()
      },
      metadata: {
        requestId: (request as any).requestId,
        reportFormat: 'json'
      }
    });
  } catch (error: any) {
    logger.error('reports error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to generate reports'
    });
  }
};
